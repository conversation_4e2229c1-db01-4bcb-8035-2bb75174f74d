#!/usr/bin/env python3
"""
调度器模块测试
测试调度器模块的导入、初始化和基本功能
"""

import unittest
import sys
import os
from unittest.mock import patch, MagicMock
import importlib

# 添加backend目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class SchedulerModuleTests(unittest.TestCase):
    """调度器模块测试"""
    
    def setUp(self):
        """测试前准备"""
        # 清理可能存在的模块缓存
        modules_to_remove = [
            'routes.scheduler_routes',
            'scheduler_manager'
        ]
        for module in modules_to_remove:
            if module in sys.modules:
                del sys.modules[module]
    
    def test_scheduler_routes_import(self):
        """测试调度器路由模块导入"""
        print("\n📦 测试调度器路由模块导入...")
        
        try:
            from routes import scheduler_routes
            self.assertIsNotNone(scheduler_routes)
            print("✅ 调度器路由模块导入成功")
            
            # 检查蓝图是否存在
            self.assertTrue(hasattr(scheduler_routes, 'scheduler_bp'))
            print("✅ 调度器蓝图存在")
            
            # 检查关键函数是否存在
            expected_functions = [
                'api_start_scheduler',
                'api_stop_scheduler', 
                'api_restart_scheduler',
                'api_get_scheduler_status',
                'api_scheduler_health'
            ]
            
            for func_name in expected_functions:
                self.assertTrue(hasattr(scheduler_routes, func_name))
                print(f"✅ 函数 {func_name} 存在")
                
        except ImportError as e:
            self.fail(f"调度器路由模块导入失败: {e}")
    
    def test_scheduler_manager_import_success(self):
        """测试调度器管理器成功导入的情况"""
        print("\n📦 测试调度器管理器成功导入...")
        
        # 模拟成功导入scheduler_manager
        mock_scheduler_manager = MagicMock()
        mock_scheduler_manager.start_scheduler = MagicMock(return_value={"success": True})
        mock_scheduler_manager.stop_scheduler = MagicMock(return_value={"success": True})
        mock_scheduler_manager.restart_scheduler = MagicMock(return_value={"success": True})
        mock_scheduler_manager.get_scheduler_status = MagicMock(return_value={"status": "running"})
        mock_scheduler_manager.refresh_personnel = MagicMock(return_value={"success": True})
        mock_scheduler_manager.make_immediate_call = MagicMock(return_value={"success": True})
        
        with patch.dict('sys.modules', {'scheduler_manager': mock_scheduler_manager}):
            # 重新导入路由模块
            import importlib
            from routes import scheduler_routes
            importlib.reload(scheduler_routes)
            
            # 验证SCHEDULER_AVAILABLE应该为True
            self.assertTrue(scheduler_routes.SCHEDULER_AVAILABLE)
            print("✅ 调度器管理器导入成功，SCHEDULER_AVAILABLE = True")
    
    def test_scheduler_manager_import_failure(self):
        """测试调度器管理器导入失败的情况"""
        print("\n❌ 测试调度器管理器导入失败...")
        
        # 模拟导入失败
        def mock_import(name, *args, **kwargs):
            if name == 'scheduler_manager':
                raise ImportError("No module named 'scheduler_manager'")
            return __import__(name, *args, **kwargs)
        
        with patch('builtins.__import__', side_effect=mock_import):
            # 重新导入路由模块
            import importlib
            from routes import scheduler_routes
            importlib.reload(scheduler_routes)
            
            # 验证SCHEDULER_AVAILABLE应该为False
            self.assertFalse(scheduler_routes.SCHEDULER_AVAILABLE)
            print("✅ 调度器管理器导入失败处理正确，SCHEDULER_AVAILABLE = False")
    
    def test_ai_call_path_configuration(self):
        """测试AI_CALL_PATH配置"""
        print("\n📁 测试AI_CALL_PATH配置...")
        
        from routes import scheduler_routes
        
        # 检查AI_CALL_PATH是否定义
        self.assertTrue(hasattr(scheduler_routes, 'AI_CALL_PATH'))
        
        ai_call_path = scheduler_routes.AI_CALL_PATH
        print(f"📍 AI_CALL_PATH: {ai_call_path}")
        
        # 检查路径是否已添加到sys.path
        self.assertIn(ai_call_path, sys.path)
        print("✅ AI_CALL_PATH已添加到sys.path")
    
    def test_scheduler_availability_check_function(self):
        """测试调度器可用性检查函数"""
        print("\n🔍 测试调度器可用性检查函数...")
        
        from routes import scheduler_routes
        
        # 测试调度器可用的情况
        with patch.object(scheduler_routes, 'SCHEDULER_AVAILABLE', True):
            result = scheduler_routes._check_scheduler_availability()
            self.assertIsNone(result)
            print("✅ 调度器可用时返回None")
        
        # 测试调度器不可用的情况
        with patch.object(scheduler_routes, 'SCHEDULER_AVAILABLE', False):
            result = scheduler_routes._check_scheduler_availability()
            self.assertIsNotNone(result)
            self.assertFalse(result['success'])
            self.assertIn('调度器模块不可用', result['message'])
            print("✅ 调度器不可用时返回错误信息")

class SchedulerModuleMockTests(unittest.TestCase):
    """使用Mock的调度器模块测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建mock的scheduler_manager
        self.mock_scheduler_manager = MagicMock()
        self.mock_scheduler_manager.start_scheduler.return_value = {"success": True, "message": "启动成功"}
        self.mock_scheduler_manager.stop_scheduler.return_value = {"success": True, "message": "停止成功"}
        self.mock_scheduler_manager.restart_scheduler.return_value = {"success": True, "message": "重启成功"}
        self.mock_scheduler_manager.get_scheduler_status.return_value = {"status": "running", "message": "运行中"}
        self.mock_scheduler_manager.refresh_personnel.return_value = {"success": True, "message": "刷新成功"}
        self.mock_scheduler_manager.make_immediate_call.return_value = {"success": True, "message": "拨号成功"}
    
    def test_mock_scheduler_functions(self):
        """测试模拟的调度器函数"""
        print("\n🎭 测试模拟的调度器函数...")
        
        with patch.dict('sys.modules', {'scheduler_manager': self.mock_scheduler_manager}):
            # 重新导入以使用mock
            import importlib
            from routes import scheduler_routes
            importlib.reload(scheduler_routes)
            
            # 测试各个函数是否可以正常调用
            functions_to_test = [
                ('start_scheduler', self.mock_scheduler_manager.start_scheduler),
                ('stop_scheduler', self.mock_scheduler_manager.stop_scheduler),
                ('restart_scheduler', self.mock_scheduler_manager.restart_scheduler),
                ('get_scheduler_status', self.mock_scheduler_manager.get_scheduler_status),
                ('refresh_personnel', self.mock_scheduler_manager.refresh_personnel),
            ]
            
            for func_name, mock_func in functions_to_test:
                # 调用函数
                result = mock_func()
                self.assertIsInstance(result, dict)
                print(f"✅ {func_name} 模拟调用成功: {result}")
    
    def test_mock_immediate_call_with_params(self):
        """测试带参数的立即拨号模拟"""
        print("\n📞 测试带参数的立即拨号模拟...")
        
        with patch.dict('sys.modules', {'scheduler_manager': self.mock_scheduler_manager}):
            # 测试不同参数组合
            test_cases = [
                ("13800138000",),
                ("13800138000", "test_call_id"),
            ]
            
            for args in test_cases:
                result = self.mock_scheduler_manager.make_immediate_call(*args)
                self.assertIsInstance(result, dict)
                self.assertTrue(result.get('success'))
                print(f"✅ 立即拨号模拟成功，参数: {args}")

class SchedulerModuleErrorHandlingTests(unittest.TestCase):
    """调度器模块错误处理测试"""
    
    def test_import_error_logging(self):
        """测试导入错误日志记录"""
        print("\n📝 测试导入错误日志记录...")
        
        with patch('routes.scheduler_routes.logging') as mock_logging:
            # 模拟导入错误
            def mock_import(name, *args, **kwargs):
                if name == 'scheduler_manager':
                    raise ImportError("测试导入错误")
                return __import__(name, *args, **kwargs)
            
            with patch('builtins.__import__', side_effect=mock_import):
                # 重新导入模块
                import importlib
                from routes import scheduler_routes
                importlib.reload(scheduler_routes)
                
                # 验证错误日志是否被记录
                mock_logging.error.assert_called()
                print("✅ 导入错误日志记录正常")
    
    def test_path_modification(self):
        """测试路径修改逻辑"""
        print("\n🛤️  测试路径修改逻辑...")
        
        # 保存原始sys.path
        original_path = sys.path.copy()
        
        try:
            # 移除AI_CALL_PATH（如果存在）
            from routes import scheduler_routes
            ai_call_path = scheduler_routes.AI_CALL_PATH
            
            if ai_call_path in sys.path:
                sys.path.remove(ai_call_path)
            
            # 重新导入模块，应该重新添加路径
            import importlib
            importlib.reload(scheduler_routes)
            
            # 验证路径是否被重新添加
            self.assertIn(ai_call_path, sys.path)
            print("✅ 路径修改逻辑正常")
            
        finally:
            # 恢复原始路径
            sys.path[:] = original_path

def run_module_tests():
    """运行模块测试"""
    print("🧪 运行调度器模块测试...")
    
    test_classes = [
        SchedulerModuleTests,
        SchedulerModuleMockTests,
        SchedulerModuleErrorHandlingTests
    ]
    
    all_success = True
    
    for test_class in test_classes:
        print(f"\n📋 运行 {test_class.__name__}...")
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        if not result.wasSuccessful():
            all_success = False
    
    return all_success

if __name__ == "__main__":
    print("🔧 调度器模块测试套件")
    print("=" * 50)
    
    success = run_module_tests()
    
    print("\n" + "=" * 50)
    print(f"📊 模块测试结果: {'🎉 全部通过' if success else '⚠️  部分失败'}")
