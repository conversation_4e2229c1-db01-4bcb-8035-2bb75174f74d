#!/usr/bin/env python3
"""
调度器启动功能测试
包括单元测试和集成测试，专门测试调度器的启动、停止、重启功能
"""

import unittest
import requests
import json
import time
import sys
import os
from unittest.mock import patch, MagicMock, Mock
import threading
import subprocess

# 添加backend目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 测试配置
TEST_CONFIG = {
    'base_url': 'http://localhost:5000/api/scheduler',
    'timeout': 30,
    'retry_count': 3,
    'retry_delay': 2
}

class SchedulerStartupUnitTests(unittest.TestCase):
    """调度器启动功能单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.base_url = TEST_CONFIG['base_url']
        
    def test_scheduler_availability_check(self):
        """测试调度器可用性检查函数"""
        # 模拟调度器不可用的情况
        with patch('routes.scheduler_routes.SCHEDULER_AVAILABLE', False):
            from routes.scheduler_routes import _check_scheduler_availability
            result = _check_scheduler_availability()
            self.assertIsNotNone(result)
            self.assertFalse(result['success'])
            self.assertIn('调度器模块不可用', result['message'])
    
    def test_scheduler_import_error_handling(self):
        """测试调度器模块导入错误处理"""
        # 模拟导入错误
        with patch('builtins.__import__', side_effect=ImportError("模块不存在")):
            # 重新导入模块来触发ImportError
            import importlib
            try:
                importlib.reload(sys.modules.get('routes.scheduler_routes'))
            except:
                pass  # 预期的错误
    
    def test_api_error_handling(self):
        """测试API错误处理"""
        # 这里可以添加更多的错误处理测试
        pass

class SchedulerStartupIntegrationTests(unittest.TestCase):
    """调度器启动功能集成测试"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.base_url = TEST_CONFIG['base_url']
        cls.timeout = TEST_CONFIG['timeout']
        cls.retry_count = TEST_CONFIG['retry_count']
        cls.retry_delay = TEST_CONFIG['retry_delay']
        
        # 检查后端服务是否运行
        cls.backend_running = cls._check_backend_service()
        if not cls.backend_running:
            print("⚠️  后端服务未运行，将跳过集成测试")
    
    @classmethod
    def _check_backend_service(cls):
        """检查后端服务是否运行"""
        try:
            response = requests.get(f"{cls.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def setUp(self):
        """每个测试前的准备"""
        if not self.backend_running:
            self.skipTest("后端服务未运行")
    
    def _make_request_with_retry(self, method, endpoint, **kwargs):
        """带重试机制的请求"""
        for attempt in range(self.retry_count):
            try:
                if method.upper() == 'GET':
                    response = requests.get(f"{self.base_url}{endpoint}", 
                                          timeout=self.timeout, **kwargs)
                elif method.upper() == 'POST':
                    response = requests.post(f"{self.base_url}{endpoint}", 
                                           timeout=self.timeout, **kwargs)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
                
                return response
            except requests.exceptions.RequestException as e:
                if attempt < self.retry_count - 1:
                    print(f"请求失败，{self.retry_delay}秒后重试... (尝试 {attempt + 1}/{self.retry_count})")
                    time.sleep(self.retry_delay)
                else:
                    raise e
    
    def test_scheduler_health_check(self):
        """测试调度器健康检查"""
        print("\n🔍 测试调度器健康检查...")
        
        response = self._make_request_with_retry('GET', '/health')
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertTrue(data.get('success'))
        self.assertIn('message', data)
        self.assertIn('scheduler_available', data)
        
        print(f"✅ 健康检查通过: {data['message']}")
        print(f"📊 调度器可用性: {data['scheduler_available']}")
    
    def test_scheduler_status_query(self):
        """测试调度器状态查询"""
        print("\n📊 测试调度器状态查询...")
        
        response = self._make_request_with_retry('GET', '/status')
        
        # 状态查询可能返回200或503，取决于调度器是否可用
        self.assertIn(response.status_code, [200, 503])
        
        data = response.json()
        if response.status_code == 200:
            print(f"✅ 状态查询成功: {data}")
            return data
        else:
            print(f"⚠️  调度器不可用: {data.get('message', '未知错误')}")
            return None
    
    def test_scheduler_start_stop_cycle(self):
        """测试调度器启动-停止循环"""
        print("\n🔄 测试调度器启动-停止循环...")
        
        # 1. 获取初始状态
        initial_status = self.test_scheduler_status_query()
        if not initial_status:
            self.skipTest("调度器模块不可用，跳过启动测试")
        
        current_status = initial_status.get('status', 'unknown')
        print(f"📋 初始状态: {current_status}")
        
        # 2. 根据当前状态决定测试流程
        if current_status == 'stopped':
            self._test_start_sequence()
        elif current_status == 'running':
            self._test_stop_sequence()
        else:
            print(f"⚠️  未知状态: {current_status}")
    
    def _test_start_sequence(self):
        """测试启动序列"""
        print("\n🚀 测试调度器启动...")
        
        # 启动调度器
        response = self._make_request_with_retry('POST', '/start')
        data = response.json()
        
        print(f"📤 启动请求响应: {data}")
        
        # 验证响应
        if response.status_code == 200:
            self.assertTrue(data.get('success'))
            print("✅ 调度器启动成功")
            
            # 等待启动完成
            time.sleep(3)
            
            # 验证状态
            status_response = self._make_request_with_retry('GET', '/status')
            if status_response.status_code == 200:
                status_data = status_response.json()
                print(f"📊 启动后状态: {status_data}")
        else:
            print(f"❌ 启动失败: {data.get('message', '未知错误')}")
    
    def _test_stop_sequence(self):
        """测试停止序列"""
        print("\n⏹️  测试调度器停止...")
        
        # 停止调度器
        response = self._make_request_with_retry('POST', '/stop')
        data = response.json()
        
        print(f"📤 停止请求响应: {data}")
        
        # 验证响应
        if response.status_code == 200:
            self.assertTrue(data.get('success'))
            print("✅ 调度器停止成功")
            
            # 等待停止完成
            time.sleep(2)
            
            # 验证状态
            status_response = self._make_request_with_retry('GET', '/status')
            if status_response.status_code == 200:
                status_data = status_response.json()
                print(f"📊 停止后状态: {status_data}")
        else:
            print(f"❌ 停止失败: {data.get('message', '未知错误')}")
    
    def test_scheduler_restart(self):
        """测试调度器重启功能"""
        print("\n🔄 测试调度器重启...")
        
        # 获取重启前状态
        initial_status = self.test_scheduler_status_query()
        if not initial_status:
            self.skipTest("调度器模块不可用，跳过重启测试")
        
        # 执行重启
        response = self._make_request_with_retry('POST', '/restart')
        data = response.json()
        
        print(f"📤 重启请求响应: {data}")
        
        if response.status_code == 200:
            self.assertTrue(data.get('success'))
            print("✅ 调度器重启成功")
            
            # 等待重启完成
            time.sleep(5)
            
            # 验证重启后状态
            status_response = self._make_request_with_retry('GET', '/status')
            if status_response.status_code == 200:
                status_data = status_response.json()
                print(f"📊 重启后状态: {status_data}")
        else:
            print(f"❌ 重启失败: {data.get('message', '未知错误')}")

class SchedulerStartupStressTests(unittest.TestCase):
    """调度器启动压力测试"""
    
    def setUp(self):
        """测试前准备"""
        self.base_url = TEST_CONFIG['base_url']
        # 检查后端服务
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code != 200:
                self.skipTest("后端服务未运行")
        except:
            self.skipTest("后端服务未运行")
    
    def test_concurrent_start_requests(self):
        """测试并发启动请求"""
        print("\n⚡ 测试并发启动请求...")
        
        def make_start_request():
            try:
                response = requests.post(f"{self.base_url}/start", timeout=10)
                return response.status_code, response.json()
            except Exception as e:
                return None, str(e)
        
        # 创建多个线程同时发送启动请求
        threads = []
        results = []
        
        for i in range(3):
            thread = threading.Thread(target=lambda: results.append(make_start_request()))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        print(f"📊 并发请求结果: {results}")
        
        # 验证至少有一个请求成功
        success_count = sum(1 for status, data in results if status == 200 and isinstance(data, dict) and data.get('success'))
        self.assertGreaterEqual(success_count, 1, "至少应有一个启动请求成功")

def run_unit_tests():
    """运行单元测试"""
    print("🧪 运行调度器启动单元测试...")
    suite = unittest.TestLoader().loadTestsFromTestCase(SchedulerStartupUnitTests)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    return result.wasSuccessful()

def run_integration_tests():
    """运行集成测试"""
    print("\n🔗 运行调度器启动集成测试...")
    suite = unittest.TestLoader().loadTestsFromTestCase(SchedulerStartupIntegrationTests)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    return result.wasSuccessful()

def run_stress_tests():
    """运行压力测试"""
    print("\n💪 运行调度器启动压力测试...")
    suite = unittest.TestLoader().loadTestsFromTestCase(SchedulerStartupStressTests)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    return result.wasSuccessful()

def run_all_tests():
    """运行所有测试"""
    print("🚀 调度器启动功能完整测试套件")
    print("=" * 60)
    
    # 运行测试
    unit_success = run_unit_tests()
    integration_success = run_integration_tests()
    stress_success = run_stress_tests()
    
    # 输出结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"单元测试: {'✅ 通过' if unit_success else '❌ 失败'}")
    print(f"集成测试: {'✅ 通过' if integration_success else '❌ 失败'}")
    print(f"压力测试: {'✅ 通过' if stress_success else '❌ 失败'}")
    
    overall_success = unit_success and integration_success and stress_success
    print(f"\n总体结果: {'🎉 全部通过' if overall_success else '⚠️  部分失败'}")
    
    return overall_success

if __name__ == "__main__":
    run_all_tests()
