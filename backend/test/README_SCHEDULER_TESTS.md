# 调度器测试套件

这是一个完整的调度器功能测试套件，包含单元测试、集成测试、性能测试和压力测试。

## 📁 测试文件结构

```
backend/test/
├── test_scheduler_startup.py      # 调度器启动功能测试
├── test_scheduler_module.py       # 调度器模块测试
├── test_scheduler_performance.py  # 调度器性能测试
├── test_scheduler_api.py          # 原有的API集成测试
├── run_scheduler_tests.py         # 测试运行器
├── test_config.py                 # 测试配置文件
└── README_SCHEDULER_TESTS.md      # 本文档
```

## 🚀 快速开始

### 1. 运行所有测试

```bash
cd backend/test
python run_scheduler_tests.py
```

### 2. 运行特定类型的测试

```bash
# 只运行模块测试
python run_scheduler_tests.py --module

# 只运行启动功能测试
python run_scheduler_tests.py --startup

# 只运行性能测试
python run_scheduler_tests.py --performance

# 只运行API测试
python run_scheduler_tests.py --api
```

### 3. 运行单个测试文件

```bash
# 运行启动功能测试
python test_scheduler_startup.py

# 运行模块测试
python test_scheduler_module.py

# 运行性能测试
python test_scheduler_performance.py

# 运行原有API测试
python test_scheduler_api.py
```

## 📋 测试类型说明

### 1. 模块测试 (test_scheduler_module.py)

测试调度器模块的导入、初始化和基本功能：

- ✅ 调度器路由模块导入测试
- ✅ 调度器管理器导入成功/失败处理测试
- ✅ AI_CALL_PATH配置测试
- ✅ 调度器可用性检查函数测试
- ✅ Mock调度器函数测试
- ✅ 错误处理和日志记录测试

### 2. 启动功能测试 (test_scheduler_startup.py)

测试调度器的启动、停止、重启功能：

**单元测试：**
- ✅ 调度器可用性检查
- ✅ 模块导入错误处理
- ✅ API错误处理

**集成测试：**
- ✅ 健康检查接口测试
- ✅ 状态查询接口测试
- ✅ 启动-停止循环测试
- ✅ 重启功能测试

**压力测试：**
- ✅ 并发启动请求测试

### 3. 性能测试 (test_scheduler_performance.py)

测试调度器在各种负载条件下的性能：

- ✅ 健康检查接口性能测试
- ✅ 状态查询接口性能测试
- ✅ 并发请求性能测试
- ✅ 持续负载测试
- ✅ 调度器操作性能测试
- ✅ 内存泄漏检测测试

### 4. API集成测试 (test_scheduler_api.py)

原有的API集成测试，测试所有调度器API接口。

## ⚙️ 配置说明

### 环境变量配置

可以通过环境变量自定义测试配置：

```bash
# 基础配置
export TEST_BASE_URL="http://localhost:5000/api/scheduler"
export TEST_BACKEND_HOST="localhost"
export TEST_BACKEND_PORT="5000"

# 超时配置
export TEST_REQUEST_TIMEOUT="30"
export TEST_HEALTH_CHECK_TIMEOUT="5"

# 重试配置
export TEST_RETRY_COUNT="3"
export TEST_RETRY_DELAY="2.0"

# 性能测试配置
export TEST_PERFORMANCE_ENABLED="true"
export TEST_PERFORMANCE_DURATION="30"

# 测试选择
export TEST_RUN_PERFORMANCE="true"
export TEST_RUN_STRESS="false"
```

### 配置文件

编辑 `test_config.py` 文件可以修改默认配置：

```python
class TestConfig:
    BASE_URL = 'http://localhost:5000/api/scheduler'
    REQUEST_TIMEOUT = 30
    RETRY_COUNT = 3
    # ... 更多配置
```

## 📊 测试报告

### 自动生成报告

测试运行器会自动生成测试报告：

```bash
# 生成默认报告
python run_scheduler_tests.py

# 指定报告文件名
python run_scheduler_tests.py --output my_test_report.txt
```

### 报告内容

测试报告包含：

- 📊 测试统计（总数、通过、失败、成功率）
- 📋 详细结果（每个测试套件的状态）
- 💡 改进建议（基于失败的测试）
- ⏱️ 执行时间统计

## 🔧 故障排除

### 常见问题

1. **后端服务未运行**
   ```
   ⚠️ 后端服务未运行，集成测试和性能测试可能会失败
   ```
   **解决方案：** 启动后端服务 `python app.py`

2. **调度器模块不可用**
   ```
   ❌ 调度器模块不可用，请检查AICall工程目录是否正确配置
   ```
   **解决方案：** 检查 `AI_CALL_PATH` 配置和调度器依赖

3. **测试超时**
   ```
   ❌ 请求超时
   ```
   **解决方案：** 增加超时时间或检查网络连接

4. **权限错误**
   ```
   ❌ 权限被拒绝
   ```
   **解决方案：** 检查文件权限或以管理员身份运行

### 跳过检查

如果需要跳过某些检查：

```bash
# 跳过后端服务检查
python run_scheduler_tests.py --skip-backend-check

# 只运行不需要后端的测试
python run_scheduler_tests.py --module
```

## 📈 性能基准

### 响应时间基准

- **健康检查接口：** < 100ms (平均)
- **状态查询接口：** < 500ms (平均)
- **启动/停止操作：** < 5000ms (平均)
- **P95响应时间：** < 2000ms
- **P99响应时间：** < 5000ms

### 成功率基准

- **单用户测试：** > 95%
- **并发测试：** > 90%
- **持续负载测试：** > 90%

### 并发性能基准

- **5个并发用户：** 响应时间增长 < 50%
- **10个并发用户：** 响应时间增长 < 100%
- **20个并发用户：** 响应时间增长 < 200%

## 🔄 持续集成

### GitHub Actions 示例

```yaml
name: Scheduler Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.8
    
    - name: Install dependencies
      run: |
        cd backend
        pip install -r requirements.txt
    
    - name: Start backend service
      run: |
        cd backend
        python app.py &
        sleep 10
    
    - name: Run scheduler tests
      run: |
        cd backend/test
        python run_scheduler_tests.py --output test_report.txt
    
    - name: Upload test report
      uses: actions/upload-artifact@v2
      with:
        name: test-report
        path: backend/test/test_report.txt
```

## 📝 开发指南

### 添加新测试

1. **创建测试文件：**
   ```python
   import unittest
   from test_config import get_test_config
   
   class MySchedulerTests(unittest.TestCase):
       def setUp(self):
           self.config = get_test_config()
       
       def test_my_feature(self):
           # 测试代码
           pass
   ```

2. **更新测试运行器：**
   在 `run_scheduler_tests.py` 中添加新的测试函数。

3. **更新配置：**
   在 `test_config.py` 中添加新的配置项。

### 测试最佳实践

1. **使用配置文件：** 从 `test_config.py` 获取配置
2. **错误处理：** 为网络请求添加适当的错误处理
3. **清理资源：** 在 `tearDown` 中清理测试资源
4. **独立性：** 确保测试之间相互独立
5. **文档：** 为测试添加清晰的文档字符串

## 📞 支持

如果遇到问题或需要帮助：

1. 查看测试输出中的错误信息
2. 检查测试报告中的建议
3. 查看本文档的故障排除部分
4. 检查后端服务日志
