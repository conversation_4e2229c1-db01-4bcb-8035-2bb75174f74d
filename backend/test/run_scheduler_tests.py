#!/usr/bin/env python3
"""
调度器测试运行器
统一运行所有调度器相关的测试
"""

import sys
import os
import argparse
import time
from datetime import datetime

# 添加backend目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def print_banner(title):
    """打印测试标题横幅"""
    print("\n" + "=" * 80)
    print(f"🚀 {title}")
    print("=" * 80)

def print_section(title):
    """打印测试章节标题"""
    print("\n" + "-" * 60)
    print(f"📋 {title}")
    print("-" * 60)

def check_backend_service():
    """检查后端服务是否运行"""
    try:
        import requests
        response = requests.get("http://localhost:5000/api/scheduler/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def run_module_tests():
    """运行模块测试"""
    print_section("调度器模块测试")
    
    try:
        from test_scheduler_module import run_module_tests
        return run_module_tests()
    except ImportError as e:
        print(f"❌ 无法导入模块测试: {e}")
        return False
    except Exception as e:
        print(f"❌ 模块测试执行失败: {e}")
        return False

def run_startup_tests():
    """运行启动功能测试"""
    print_section("调度器启动功能测试")
    
    try:
        from test_scheduler_startup import run_all_tests
        return run_all_tests()
    except ImportError as e:
        print(f"❌ 无法导入启动测试: {e}")
        return False
    except Exception as e:
        print(f"❌ 启动测试执行失败: {e}")
        return False

def run_performance_tests():
    """运行性能测试"""
    print_section("调度器性能测试")
    
    try:
        from test_scheduler_performance import run_performance_tests
        return run_performance_tests()
    except ImportError as e:
        print(f"❌ 无法导入性能测试: {e}")
        return False
    except Exception as e:
        print(f"❌ 性能测试执行失败: {e}")
        return False

def run_api_tests():
    """运行原有的API测试"""
    print_section("调度器API集成测试")
    
    try:
        # 导入并运行原有的API测试
        import subprocess
        result = subprocess.run([
            sys.executable, 
            os.path.join(os.path.dirname(__file__), 'test_scheduler_api.py')
        ], capture_output=True, text=True, timeout=120)
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("❌ API测试超时")
        return False
    except Exception as e:
        print(f"❌ API测试执行失败: {e}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    print_section("测试报告")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    
    print(f"📊 测试统计:")
    print(f"  总测试套件: {total_tests}")
    print(f"  通过: {passed_tests}")
    print(f"  失败: {failed_tests}")
    print(f"  成功率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    # 生成建议
    print(f"\n💡 建议:")
    if failed_tests == 0:
        print("  🎉 所有测试都通过了！调度器功能正常。")
    else:
        print("  ⚠️  有测试失败，请检查:")
        for test_name, result in results.items():
            if not result:
                print(f"    - {test_name}")
        
        if not results.get('模块测试', True):
            print("  📦 模块测试失败可能表示调度器依赖有问题")
        
        if not results.get('启动功能测试', True):
            print("  🚀 启动功能测试失败可能表示调度器启动逻辑有问题")
        
        if not results.get('性能测试', True):
            print("  ⚡ 性能测试失败可能表示系统负载过高或有性能问题")
        
        if not results.get('API测试', True):
            print("  🔌 API测试失败可能表示后端服务有问题")

def save_test_report(results, output_file=None):
    """保存测试报告到文件"""
    if not output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"scheduler_test_report_{timestamp}.txt"
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("调度器测试报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            total_tests = len(results)
            passed_tests = sum(1 for result in results.values() if result)
            
            f.write(f"测试统计:\n")
            f.write(f"  总测试套件: {total_tests}\n")
            f.write(f"  通过: {passed_tests}\n")
            f.write(f"  失败: {total_tests - passed_tests}\n")
            f.write(f"  成功率: {passed_tests/total_tests*100:.1f}%\n\n")
            
            f.write("详细结果:\n")
            for test_name, result in results.items():
                status = "通过" if result else "失败"
                f.write(f"  {test_name}: {status}\n")
        
        print(f"📄 测试报告已保存到: {output_file}")
        return True
    except Exception as e:
        print(f"❌ 保存测试报告失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="调度器测试运行器")
    parser.add_argument("--module", action="store_true", help="只运行模块测试")
    parser.add_argument("--startup", action="store_true", help="只运行启动功能测试")
    parser.add_argument("--performance", action="store_true", help="只运行性能测试")
    parser.add_argument("--api", action="store_true", help="只运行API测试")
    parser.add_argument("--skip-backend-check", action="store_true", help="跳过后端服务检查")
    parser.add_argument("--output", type=str, help="测试报告输出文件")
    parser.add_argument("--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 打印开始信息
    print_banner("调度器测试套件")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查后端服务（除非跳过）
    if not args.skip_backend_check:
        print("\n🔍 检查后端服务状态...")
        backend_running = check_backend_service()
        if backend_running:
            print("✅ 后端服务正在运行")
        else:
            print("⚠️  后端服务未运行，集成测试和性能测试可能会失败")
    
    # 确定要运行的测试
    run_all = not any([args.module, args.startup, args.performance, args.api])
    
    results = {}
    start_time = time.time()
    
    # 运行测试
    if run_all or args.module:
        results['模块测试'] = run_module_tests()
    
    if run_all or args.startup:
        results['启动功能测试'] = run_startup_tests()
    
    if run_all or args.performance:
        results['性能测试'] = run_performance_tests()
    
    if run_all or args.api:
        results['API测试'] = run_api_tests()
    
    # 计算总耗时
    end_time = time.time()
    total_time = end_time - start_time
    
    # 生成报告
    generate_test_report(results)
    
    # 保存报告
    if args.output or len(results) > 1:
        save_test_report(results, args.output)
    
    # 打印结束信息
    print(f"\n🕐 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⏱️  总耗时: {total_time:.2f}秒")
    
    # 返回退出码
    all_passed = all(results.values())
    if all_passed:
        print("\n🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败！")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"\n\n❌ 测试运行器发生错误: {e}")
        sys.exit(1)
