#!/usr/bin/env python3
"""
调度器测试配置文件
集中管理所有测试的配置参数
"""

import os

class TestConfig:
    """测试配置类"""
    
    # 基础配置
    BASE_URL = os.getenv('TEST_BASE_URL', 'http://localhost:5000/api/scheduler')
    BACKEND_HOST = os.getenv('TEST_BACKEND_HOST', 'localhost')
    BACKEND_PORT = int(os.getenv('TEST_BACKEND_PORT', 5000))
    
    # 超时配置
    REQUEST_TIMEOUT = int(os.getenv('TEST_REQUEST_TIMEOUT', 30))
    HEALTH_CHECK_TIMEOUT = int(os.getenv('TEST_HEALTH_CHECK_TIMEOUT', 5))
    OPERATION_TIMEOUT = int(os.getenv('TEST_OPERATION_TIMEOUT', 60))
    
    # 重试配置
    RETRY_COUNT = int(os.getenv('TEST_RETRY_COUNT', 3))
    RETRY_DELAY = float(os.getenv('TEST_RETRY_DELAY', 2.0))
    
    # 性能测试配置
    PERFORMANCE_TEST_ENABLED = os.getenv('TEST_PERFORMANCE_ENABLED', 'true').lower() == 'true'
    CONCURRENT_USERS = [1, 5, 10, 20]
    PERFORMANCE_TEST_DURATION = int(os.getenv('TEST_PERFORMANCE_DURATION', 30))
    WARMUP_REQUESTS = int(os.getenv('TEST_WARMUP_REQUESTS', 5))
    
    # 负载测试配置
    LOAD_TEST_ENABLED = os.getenv('TEST_LOAD_ENABLED', 'true').lower() == 'true'
    LOAD_TEST_REQUESTS = int(os.getenv('TEST_LOAD_REQUESTS', 100))
    SUSTAINED_LOAD_DURATION = int(os.getenv('TEST_SUSTAINED_LOAD_DURATION', 10))
    
    # 测试数据配置
    TEST_PHONE_NUMBER = os.getenv('TEST_PHONE_NUMBER', '13800138000')
    TEST_CALL_ID = os.getenv('TEST_CALL_ID', 'test_call_001')
    
    # 日志配置
    LOG_LEVEL = os.getenv('TEST_LOG_LEVEL', 'INFO')
    VERBOSE_OUTPUT = os.getenv('TEST_VERBOSE', 'false').lower() == 'true'
    
    # 报告配置
    GENERATE_REPORT = os.getenv('TEST_GENERATE_REPORT', 'true').lower() == 'true'
    REPORT_FORMAT = os.getenv('TEST_REPORT_FORMAT', 'text')  # text, json, html
    REPORT_OUTPUT_DIR = os.getenv('TEST_REPORT_OUTPUT_DIR', './test_reports')
    
    # 环境检查配置
    SKIP_BACKEND_CHECK = os.getenv('TEST_SKIP_BACKEND_CHECK', 'false').lower() == 'true'
    SKIP_DEPENDENCY_CHECK = os.getenv('TEST_SKIP_DEPENDENCY_CHECK', 'false').lower() == 'true'
    
    # 测试选择配置
    RUN_UNIT_TESTS = os.getenv('TEST_RUN_UNIT', 'true').lower() == 'true'
    RUN_INTEGRATION_TESTS = os.getenv('TEST_RUN_INTEGRATION', 'true').lower() == 'true'
    RUN_PERFORMANCE_TESTS = os.getenv('TEST_RUN_PERFORMANCE', 'true').lower() == 'true'
    RUN_STRESS_TESTS = os.getenv('TEST_RUN_STRESS', 'false').lower() == 'true'
    
    @classmethod
    def get_api_url(cls, endpoint=''):
        """获取完整的API URL"""
        return f"{cls.BASE_URL}{endpoint}"
    
    @classmethod
    def get_backend_url(cls):
        """获取后端基础URL"""
        return f"http://{cls.BACKEND_HOST}:{cls.BACKEND_PORT}"
    
    @classmethod
    def print_config(cls):
        """打印当前配置"""
        print("📋 测试配置:")
        print(f"  基础URL: {cls.BASE_URL}")
        print(f"  后端地址: {cls.get_backend_url()}")
        print(f"  请求超时: {cls.REQUEST_TIMEOUT}秒")
        print(f"  重试次数: {cls.RETRY_COUNT}")
        print(f"  重试延迟: {cls.RETRY_DELAY}秒")
        print(f"  性能测试: {'启用' if cls.PERFORMANCE_TEST_ENABLED else '禁用'}")
        print(f"  负载测试: {'启用' if cls.LOAD_TEST_ENABLED else '禁用'}")
        print(f"  详细输出: {'启用' if cls.VERBOSE_OUTPUT else '禁用'}")
        print(f"  生成报告: {'启用' if cls.GENERATE_REPORT else '禁用'}")

class PerformanceThresholds:
    """性能测试阈值配置"""
    
    # 响应时间阈值（毫秒）
    MAX_AVG_RESPONSE_TIME = 1000
    MAX_P95_RESPONSE_TIME = 2000
    MAX_P99_RESPONSE_TIME = 5000
    
    # 成功率阈值
    MIN_SUCCESS_RATE = 95.0
    
    # 并发测试阈值
    MAX_CONCURRENT_RESPONSE_TIME = 2000
    MIN_CONCURRENT_SUCCESS_RATE = 90.0
    
    # 负载测试阈值
    MAX_LOAD_AVG_RESPONSE_TIME = 1500
    MAX_MEMORY_GROWTH_RATE = 50.0  # 百分比
    
    @classmethod
    def check_response_time(cls, avg_time, p95_time, p99_time):
        """检查响应时间是否在阈值内"""
        issues = []
        
        if avg_time > cls.MAX_AVG_RESPONSE_TIME:
            issues.append(f"平均响应时间超标: {avg_time:.2f}ms > {cls.MAX_AVG_RESPONSE_TIME}ms")
        
        if p95_time > cls.MAX_P95_RESPONSE_TIME:
            issues.append(f"P95响应时间超标: {p95_time:.2f}ms > {cls.MAX_P95_RESPONSE_TIME}ms")
        
        if p99_time > cls.MAX_P99_RESPONSE_TIME:
            issues.append(f"P99响应时间超标: {p99_time:.2f}ms > {cls.MAX_P99_RESPONSE_TIME}ms")
        
        return issues
    
    @classmethod
    def check_success_rate(cls, success_rate, is_concurrent=False):
        """检查成功率是否在阈值内"""
        threshold = cls.MIN_CONCURRENT_SUCCESS_RATE if is_concurrent else cls.MIN_SUCCESS_RATE
        
        if success_rate < threshold:
            return [f"成功率过低: {success_rate:.2f}% < {threshold}%"]
        
        return []

class TestData:
    """测试数据配置"""
    
    # 测试用的电话号码
    VALID_PHONE_NUMBERS = [
        '13800138000',
        '13800138001',
        '13800138002'
    ]
    
    # 无效的电话号码（用于负面测试）
    INVALID_PHONE_NUMBERS = [
        '',
        '123',
        'invalid',
        '1' * 20
    ]
    
    # 测试用的调用ID
    TEST_CALL_IDS = [
        'test_call_001',
        'test_call_002',
        'test_call_003'
    ]
    
    # 测试用的JSON数据
    VALID_IMMEDIATE_CALL_DATA = [
        {'phone': '13800138000'},
        {'phone': '13800138001', 'obj_id': 'test_call_001'},
        {'phone': '13800138002', 'obj_id': 'test_call_002'}
    ]
    
    # 无效的JSON数据
    INVALID_IMMEDIATE_CALL_DATA = [
        {},  # 缺少phone字段
        {'phone': ''},  # 空phone字段
        {'phone': 'invalid'},  # 无效phone格式
        {'obj_id': 'test_call_001'}  # 只有obj_id没有phone
    ]

class TestMessages:
    """测试消息配置"""
    
    # 成功消息
    SUCCESS_MESSAGES = {
        'health_check': '调度器API正常',
        'start_success': '启动成功',
        'stop_success': '停止成功',
        'restart_success': '重启成功'
    }
    
    # 错误消息
    ERROR_MESSAGES = {
        'module_unavailable': '调度器模块不可用',
        'missing_phone': '缺少手机号参数',
        'invalid_phone': '手机号格式不正确',
        'request_format_error': '请求数据格式错误'
    }
    
    # 状态消息
    STATUS_MESSAGES = {
        'running': '运行中',
        'stopped': '已停止',
        'starting': '启动中',
        'stopping': '停止中'
    }

# 导出配置实例
config = TestConfig()
thresholds = PerformanceThresholds()
test_data = TestData()
messages = TestMessages()

# 便捷函数
def get_test_config():
    """获取测试配置实例"""
    return config

def get_performance_thresholds():
    """获取性能阈值配置实例"""
    return thresholds

def get_test_data():
    """获取测试数据实例"""
    return test_data

def get_test_messages():
    """获取测试消息实例"""
    return messages

if __name__ == "__main__":
    # 打印配置信息
    config.print_config()
    
    print(f"\n📊 性能阈值:")
    print(f"  最大平均响应时间: {thresholds.MAX_AVG_RESPONSE_TIME}ms")
    print(f"  最大P95响应时间: {thresholds.MAX_P95_RESPONSE_TIME}ms")
    print(f"  最小成功率: {thresholds.MIN_SUCCESS_RATE}%")
    
    print(f"\n📞 测试数据:")
    print(f"  有效电话号码: {test_data.VALID_PHONE_NUMBERS}")
    print(f"  测试调用ID: {test_data.TEST_CALL_IDS}")
