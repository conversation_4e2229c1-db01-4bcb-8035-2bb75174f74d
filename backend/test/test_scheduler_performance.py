#!/usr/bin/env python3
"""
调度器性能测试
测试调度器在各种负载条件下的性能表现
"""

import unittest
import requests
import time
import threading
import statistics
import sys
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import json

# 添加backend目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 性能测试配置
PERFORMANCE_CONFIG = {
    'base_url': 'http://localhost:5000/api/scheduler',
    'concurrent_users': [1, 5, 10, 20],
    'test_duration': 30,  # 秒
    'request_timeout': 10,
    'warmup_requests': 5
}

class SchedulerPerformanceTests(unittest.TestCase):
    """调度器性能测试"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.base_url = PERFORMANCE_CONFIG['base_url']
        cls.timeout = PERFORMANCE_CONFIG['request_timeout']
        
        # 检查后端服务是否运行
        cls.backend_running = cls._check_backend_service()
        if not cls.backend_running:
            print("⚠️  后端服务未运行，将跳过性能测试")
    
    @classmethod
    def _check_backend_service(cls):
        """检查后端服务是否运行"""
        try:
            response = requests.get(f"{cls.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def setUp(self):
        """每个测试前的准备"""
        if not self.backend_running:
            self.skipTest("后端服务未运行")
        
        # 预热请求
        self._warmup()
    
    def _warmup(self):
        """预热请求"""
        print("🔥 执行预热请求...")
        for _ in range(PERFORMANCE_CONFIG['warmup_requests']):
            try:
                requests.get(f"{self.base_url}/health", timeout=self.timeout)
                time.sleep(0.1)
            except:
                pass
    
    def _measure_request_time(self, method, endpoint, **kwargs):
        """测量单个请求的响应时间"""
        start_time = time.time()
        try:
            if method.upper() == 'GET':
                response = requests.get(f"{self.base_url}{endpoint}", 
                                      timeout=self.timeout, **kwargs)
            elif method.upper() == 'POST':
                response = requests.post(f"{self.base_url}{endpoint}", 
                                       timeout=self.timeout, **kwargs)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            return {
                'success': True,
                'response_time': response_time,
                'status_code': response.status_code,
                'response_size': len(response.content)
            }
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            return {
                'success': False,
                'response_time': response_time,
                'error': str(e)
            }
    
    def test_health_check_performance(self):
        """测试健康检查接口性能"""
        print("\n💓 测试健康检查接口性能...")
        
        results = []
        test_count = 100
        
        for i in range(test_count):
            result = self._measure_request_time('GET', '/health')
            results.append(result)
            
            if (i + 1) % 20 == 0:
                print(f"完成 {i + 1}/{test_count} 个请求")
        
        # 分析结果
        self._analyze_performance_results(results, "健康检查")
    
    def test_status_query_performance(self):
        """测试状态查询接口性能"""
        print("\n📊 测试状态查询接口性能...")
        
        results = []
        test_count = 50
        
        for i in range(test_count):
            result = self._measure_request_time('GET', '/status')
            results.append(result)
            
            if (i + 1) % 10 == 0:
                print(f"完成 {i + 1}/{test_count} 个请求")
        
        # 分析结果
        self._analyze_performance_results(results, "状态查询")
    
    def test_concurrent_health_checks(self):
        """测试并发健康检查"""
        print("\n⚡ 测试并发健康检查...")
        
        for concurrent_users in PERFORMANCE_CONFIG['concurrent_users']:
            print(f"\n👥 测试 {concurrent_users} 个并发用户...")
            
            results = []
            
            def make_request():
                return self._measure_request_time('GET', '/health')
            
            # 使用线程池执行并发请求
            with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
                futures = [executor.submit(make_request) for _ in range(concurrent_users * 5)]
                
                for future in as_completed(futures):
                    result = future.result()
                    results.append(result)
            
            # 分析结果
            self._analyze_performance_results(results, f"并发健康检查({concurrent_users}用户)")
    
    def test_sustained_load(self):
        """测试持续负载"""
        print("\n🏃 测试持续负载...")
        
        duration = 10  # 10秒测试
        concurrent_users = 5
        results = []
        
        def worker():
            end_time = time.time() + duration
            while time.time() < end_time:
                result = self._measure_request_time('GET', '/health')
                results.append(result)
                time.sleep(0.1)  # 100ms间隔
        
        # 启动多个工作线程
        threads = []
        for _ in range(concurrent_users):
            thread = threading.Thread(target=worker)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 分析结果
        self._analyze_performance_results(results, f"持续负载({duration}秒)")
    
    def test_scheduler_operation_performance(self):
        """测试调度器操作性能"""
        print("\n🔧 测试调度器操作性能...")
        
        operations = [
            ('GET', '/status', '状态查询'),
            ('POST', '/refresh', '刷新人员'),
        ]
        
        for method, endpoint, operation_name in operations:
            print(f"\n🔍 测试 {operation_name} 性能...")
            
            results = []
            test_count = 10
            
            for i in range(test_count):
                result = self._measure_request_time(method, endpoint)
                results.append(result)
                time.sleep(0.5)  # 操作间隔
            
            # 分析结果
            self._analyze_performance_results(results, operation_name)
    
    def _analyze_performance_results(self, results, test_name):
        """分析性能测试结果"""
        if not results:
            print(f"❌ {test_name}: 没有有效结果")
            return
        
        # 分离成功和失败的请求
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]
        
        if not successful_results:
            print(f"❌ {test_name}: 所有请求都失败了")
            return
        
        # 计算统计数据
        response_times = [r['response_time'] for r in successful_results]
        
        stats = {
            'total_requests': len(results),
            'successful_requests': len(successful_results),
            'failed_requests': len(failed_results),
            'success_rate': len(successful_results) / len(results) * 100,
            'avg_response_time': statistics.mean(response_times),
            'min_response_time': min(response_times),
            'max_response_time': max(response_times),
            'median_response_time': statistics.median(response_times),
        }
        
        if len(response_times) > 1:
            stats['std_response_time'] = statistics.stdev(response_times)
        else:
            stats['std_response_time'] = 0
        
        # 计算百分位数
        sorted_times = sorted(response_times)
        stats['p95_response_time'] = sorted_times[int(len(sorted_times) * 0.95)]
        stats['p99_response_time'] = sorted_times[int(len(sorted_times) * 0.99)]
        
        # 输出结果
        print(f"\n📊 {test_name} 性能测试结果:")
        print(f"  总请求数: {stats['total_requests']}")
        print(f"  成功请求: {stats['successful_requests']}")
        print(f"  失败请求: {stats['failed_requests']}")
        print(f"  成功率: {stats['success_rate']:.2f}%")
        print(f"  平均响应时间: {stats['avg_response_time']:.2f}ms")
        print(f"  最小响应时间: {stats['min_response_time']:.2f}ms")
        print(f"  最大响应时间: {stats['max_response_time']:.2f}ms")
        print(f"  中位数响应时间: {stats['median_response_time']:.2f}ms")
        print(f"  标准差: {stats['std_response_time']:.2f}ms")
        print(f"  95%百分位: {stats['p95_response_time']:.2f}ms")
        print(f"  99%百分位: {stats['p99_response_time']:.2f}ms")
        
        # 性能评估
        self._evaluate_performance(stats, test_name)
        
        return stats
    
    def _evaluate_performance(self, stats, test_name):
        """评估性能表现"""
        issues = []
        
        # 检查成功率
        if stats['success_rate'] < 95:
            issues.append(f"成功率过低: {stats['success_rate']:.2f}%")
        
        # 检查平均响应时间
        if stats['avg_response_time'] > 1000:
            issues.append(f"平均响应时间过长: {stats['avg_response_time']:.2f}ms")
        
        # 检查95%百分位响应时间
        if stats['p95_response_time'] > 2000:
            issues.append(f"95%百分位响应时间过长: {stats['p95_response_time']:.2f}ms")
        
        # 检查响应时间变异性
        if stats['std_response_time'] > stats['avg_response_time']:
            issues.append(f"响应时间变异性过大: 标准差{stats['std_response_time']:.2f}ms")
        
        if issues:
            print(f"⚠️  {test_name} 性能问题:")
            for issue in issues:
                print(f"    - {issue}")
        else:
            print(f"✅ {test_name} 性能表现良好")

class SchedulerLoadTests(unittest.TestCase):
    """调度器负载测试"""
    
    def setUp(self):
        """测试前准备"""
        self.base_url = PERFORMANCE_CONFIG['base_url']
        
        # 检查后端服务
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code != 200:
                self.skipTest("后端服务未运行")
        except:
            self.skipTest("后端服务未运行")
    
    def test_memory_leak_detection(self):
        """测试内存泄漏检测"""
        print("\n🧠 测试内存泄漏检测...")
        
        # 执行大量请求，观察响应时间是否持续增长
        request_count = 100
        response_times = []
        
        for i in range(request_count):
            start_time = time.time()
            try:
                response = requests.get(f"{self.base_url}/health", timeout=10)
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                response_times.append(response_time)
                
                if (i + 1) % 20 == 0:
                    avg_time = statistics.mean(response_times[-20:])
                    print(f"请求 {i + 1}: 最近20次平均响应时间 {avg_time:.2f}ms")
                    
            except Exception as e:
                print(f"请求 {i + 1} 失败: {e}")
        
        # 分析趋势
        if len(response_times) >= 20:
            first_half = response_times[:len(response_times)//2]
            second_half = response_times[len(response_times)//2:]
            
            first_avg = statistics.mean(first_half)
            second_avg = statistics.mean(second_half)
            
            print(f"前半段平均响应时间: {first_avg:.2f}ms")
            print(f"后半段平均响应时间: {second_avg:.2f}ms")
            
            if second_avg > first_avg * 1.5:
                print("⚠️  可能存在内存泄漏或性能退化")
            else:
                print("✅ 未检测到明显的性能退化")

def run_performance_tests():
    """运行性能测试"""
    print("🚀 运行调度器性能测试...")
    
    test_classes = [
        SchedulerPerformanceTests,
        SchedulerLoadTests
    ]
    
    all_success = True
    
    for test_class in test_classes:
        print(f"\n📋 运行 {test_class.__name__}...")
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        if not result.wasSuccessful():
            all_success = False
    
    return all_success

if __name__ == "__main__":
    print("⚡ 调度器性能测试套件")
    print("=" * 50)
    
    success = run_performance_tests()
    
    print("\n" + "=" * 50)
    print(f"📊 性能测试结果: {'🎉 全部通过' if success else '⚠️  部分失败'}")
