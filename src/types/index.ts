// 患者信息类型
export interface Patient {
  _id: string;
  patient_id: string; // 患者编号，由医生填入
  name: string;
  phone: string;
  age: number;
  gender: '男' | '女';
  enrollment_date: string;
  training_status: '未开始' | '训练中' | '暂停' | '终止' | '休息';
  assigned_doctor?: string; // 分配的医生用户名
  assigned_doctor_name?: string; // 分配的医生姓名
  assignment_date?: string; // 医生分配日期
  exercise_plan_id?: string; // 关联的锻炼计划ID
  exercise_plan_name?: string; // 关联的锻炼计划名称
  // 动态添加的字段
  callStatus?: string;
  callStatusType?: 'active' | 'pending' | 'inactive' | 'completed';
  yesterdayTraining?: string; // 实际存储的是今日最高训练次数
  needsDoctorIntervention?: string;
  todayCallCount?: number;
}

// 锻炼计划类型
export interface ExercisePlan {
  _id: string;
  plan_id: string;
  patient_id: string;
  patient_name: string;
  doctor_id: string;
  doctor_name: string;

  plan_name: string;
  description: string;
  status: 'active' | 'completed' | 'paused' | 'cancelled';

  total_weeks: number;
  initial_daily_count: number;
  weekly_increment: number;
  max_rest_days_per_week: number;

  start_date: string;
  end_date: string;
  current_week: number;
  current_daily_target: number;

  weekly_records: WeeklyRecord[];

  total_target_count: number;
  total_actual_count: number;
  overall_completion_rate: number;
  consecutive_days: number;
  total_rest_days: number;

  created_at: string;
  updated_at: string;
  created_by: string;
  last_modified_by: string;
}

// 锻炼计划选项类型（用于下拉框）
export interface ExercisePlanOption {
  _id: string;
  plan_name: string;
  description: string;
  status: 'active' | 'completed' | 'paused' | 'cancelled';
}

// 每周记录类型
export interface WeeklyRecord {
  week_number: number;
  week_start_date: string;
  week_end_date: string;
  target_daily_count: number;
  daily_records: DailyRecord[];
  week_completion_rate: number;
  rest_days_used: number;
}

// 每日记录类型
export interface DailyRecord {
  date: string;
  day_of_week: number;
  target_count: number;
  actual_count: number;
  is_rest_day: boolean;
  completion_rate: number;
  notes: string;
}

// 通话记录类型
export interface CallRecord {
  _id: string;
  患者名字?: string;
  手机号?: string;
  记录日期?: string;
  通话时间?: string;
  拨号状态?: string;
  训练完成情况?: string;
  训练次数?: string;
  训练时长?: string;
  依从性?: string;
  是否有不适感?: string;
  不适感内容?: string;
  锻炼后是否有缓解?: string;
  锻炼辅助仪器是否有问题?: string;
  锻炼辅助仪器问题内容?: string;
  设备使用情况?: string;
  是否需要医生人工和患者联系?: string;
  医生建议?: string;
  康复效果评估?: string;
  疼痛改善情况?: string;
  活动能力变化?: string;
  对话历史记录?: Array<{
    role: 'assistant' | 'user';
    content: string;
  }>;
}

// 统计数据类型
export interface StatsData {
  totalPersonnel: number;
  pendingToday: number;
  completedToday: number;
  avgTraining: number;
  completionRate: number;
  pendingTrend?: TrendData;
  completedTrend?: TrendData;
  completionRateTrend?: TrendData;
  avgTrainingTrend?: TrendData;
}

// 趋势数据类型
export interface TrendData {
  change: number;
  isUp: boolean;
  text: string;
}

// 康复进度类型
export interface ProgressData {
  currentDay: number;
  totalDays: number;
  compliance: number;
  todayTraining: number;
  avgTraining: number;
  totalCallDays?: number;
  totalTrainingDays?: number;
  records?: Array<{
    date: string;
    training: number;
    completed: boolean;
  }>;
}

// 训练统计数据类型
export interface TrainingStats {
  completedCount: number;
  totalCount: number;
  avgTrainingCount: number;
  minTrainingCount: number;
  maxTrainingCount: number;
  discomfortCount: number;
  deviceIssueCount: number;
  doctorInterventionCount: number;
}

// 筛选条件类型
export interface FilterOptions {
  startDate: string;
  endDate: string;
  patientIds: string;
  doctorIntervention: string;
}

// 随访统计数据接口
export interface FollowUpStat {
  time: string;
  time_key: string;
  total_follow_up_people: number;
  total_calls: number;
  connected_people: number;
  training_people_count: number;
  doctor_intervention_people: number;
} 